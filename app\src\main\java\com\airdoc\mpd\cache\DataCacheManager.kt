package com.airdoc.mpd.cache

import android.content.Context
import com.airdoc.component.common.cache.MMKVManager
import com.airdoc.component.common.log.Logger
import com.airdoc.component.common.net.collectResponse
import com.airdoc.mpd.common.CommonPreference
import com.airdoc.mpd.detection.bean.*
import com.airdoc.mpd.detection.repository.SignalResultRepository
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.MutableStateFlow
import org.json.JSONArray
import org.json.JSONObject
import java.util.concurrent.atomic.AtomicBoolean
import kotlin.coroutines.resume

/**
 * FileName: DataCacheManager
 * Author: AI Assistant
 * Date: 2025/1/21
 * PS: 数据缓存管理器，负责自动定时上传数据缓存
 */
object DataCacheManager {

    private val TAG = DataCacheManager::class.java.simpleName
    
    // 协程作用域
    private val coroutineScope = CoroutineScope(Dispatchers.Main + SupervisorJob())
    
    // 定时上传任务
    private var uploadJob: Job? = null
    
    // 是否正在上传
    private val isUploading = AtomicBoolean(false)
    
    // 上传间隔时间（毫秒）- 默认30分钟
    private const val UPLOAD_INTERVAL = 30 * 60 * 1000L
    
    // 最小上传间隔时间（毫秒）- 防止频繁上传
    private const val MIN_UPLOAD_INTERVAL = 5 * 60 * 1000L
    
    // 上次上传时间
    private var lastUploadTime = 0L

    // SignalResult数据仓库
    private val signalResultRepository by lazy { SignalResultRepository() }

    private val gson = Gson()

    /**
     * 启动自动上传服务
     */
    fun startAutoUpload(context: Context) {
        Logger.d(TAG, msg = "启动数据缓存自动上传服务")
        
        // 取消之前的任务
        stopAutoUpload()
        
        // 启动定时上传任务
        uploadJob = coroutineScope.launch {
            while (isActive) {
                try {
                    // 检查是否有数据需要上传
                    val cacheDataCount = getCacheDataCount()
                    if (cacheDataCount > 0) {
                        Logger.d(TAG, msg = "检测到 $cacheDataCount 条缓存数据，准备自动上传")
                        
                        // 检查上传间隔
                        val currentTime = System.currentTimeMillis()
                        if (currentTime - lastUploadTime >= MIN_UPLOAD_INTERVAL) {
                            tryAutoUpload(context)
                        } else {
                            Logger.d(TAG, msg = "距离上次上传时间过短，跳过本次上传")
                        }
                    }
                    
                    // 等待下次检查
                    delay(UPLOAD_INTERVAL)
                    
                } catch (e: Exception) {
                    Logger.e(TAG, msg = "自动上传任务异常: ${e.message}")
                    // 发生异常时等待较短时间后重试
                    delay(MIN_UPLOAD_INTERVAL)
                }
            }
        }
    }

    /**
     * 停止自动上传服务
     */
    fun stopAutoUpload() {
        Logger.d(TAG, msg = "停止数据缓存自动上传服务")
        uploadJob?.cancel()
        uploadJob = null
    }

    /**
     * 尝试自动上传
     */
    private suspend fun tryAutoUpload(context: Context) {
        if (isUploading.get()) {
            Logger.d(TAG, msg = "正在上传中，跳过本次自动上传")
            return
        }

        try {
            isUploading.set(true)
            Logger.d(TAG, msg = "开始自动上传数据缓存...")

            // 从MMKV获取并上传SignalResult数据
            val uploadSuccess = withContext(Dispatchers.IO) {
                uploadSignalResultsFromCache()
            }

            if (uploadSuccess) {
                // 上传成功后清理缓存数据
//                clearDataCache()
                lastUploadTime = System.currentTimeMillis()
                Logger.d(TAG, msg = "自动上传数据缓存完成")
            } else {
                Logger.w(TAG, msg = "自动上传数据缓存部分失败")
            }

        } catch (e: Exception) {
            Logger.e(TAG, msg = "自动上传数据缓存失败: ${e.message}")
        } finally {
            isUploading.set(false)
        }
    }

    /**
     * 手动上传数据缓存
     */
    suspend fun manualUpload(context: Context): Boolean {
        return try {
            if (isUploading.get()) {
                Logger.d(TAG, msg = "正在上传中，请稍后再试")
                false
            } else {
                tryAutoUpload(context)
                true
            }
        } catch (e: Exception) {
            Logger.e(TAG, msg = "手动上传异常: ${e.message}")
            false
        }
    }

    /**
     * 获取缓存数据条数
     */
    fun getCacheDataCount(): Int {
        var count = 0
        try {
            // 获取HRV评估结果数据条数
            val hrvData = MMKVManager.decodeString(CommonPreference.HRV_ASSESSMENT_RESULT)
            if (!hrvData.isNullOrEmpty()) {
                try {
                    val jsonObject = JSONObject(hrvData)
                    if (jsonObject.has("totalCount")) {
                        count += jsonObject.getInt("totalCount")
                    } else if (jsonObject.has("assessmentResults")) {
                        val resultArray = jsonObject.getJSONArray("assessmentResults")
                        count += resultArray.length()
                    } else {
                        // 如果是旧格式的单个对象，计为1条
                        count += 1
                    }
                } catch (e: Exception) {
                    Logger.w(TAG, msg = "解析HRV数据异常，按1条计算: ${e.message}")
                    count += 1
                }
            }
            
            // 这里可以添加其他类型的缓存数据统计
            // 例如：其他检测结果、用户数据等
            
        } catch (e: Exception) {
            Logger.e(TAG, msg = "计算缓存数据条数异常: ${e.message}")
        }
        return count
    }

    /**
     * 清理数据缓存
     */
    private fun clearDataCache() {
        try {
            Logger.d(TAG, msg = "开始清理数据缓存...")

            // 清理HRV评估结果数据
            MMKVManager.encodeString(CommonPreference.HRV_ASSESSMENT_RESULT, null)
            Logger.d(TAG, msg = "已清理HRV评估结果数据")

            // 这里可以添加其他类型的缓存数据清理
            // 例如：其他检测结果、用户数据等

            Logger.d(TAG, msg = "数据缓存清理完成")

        } catch (e: Exception) {
            Logger.e(TAG, msg = "清理数据缓存异常: ${e.message}")
        }
    }

    /**
     * 检查是否正在上传
     */
    fun isUploading(): Boolean {
        return isUploading.get()
    }

    /**
     * 获取上次上传时间
     */
    fun getLastUploadTime(): Long {
        return lastUploadTime
    }

    /**
     * 释放资源
     */
    fun release() {
        stopAutoUpload()
        coroutineScope.cancel()
    }

    /**
     * 从MMKV缓存中上传SignalResult数据
     */
    private suspend fun uploadSignalResultsFromCache(): Boolean {
        return try {
            Logger.d(TAG, msg = "开始从MMKV缓存上传SignalResult数据")

            // 从MMKV获取HRV评估结果数据
            val hrvData = MMKVManager.decodeString(CommonPreference.HRV_ASSESSMENT_RESULT)

            if (hrvData.isNullOrEmpty()) {
                Logger.w(TAG, msg = "HRV评估结果数据为空，无需上传")
                return true
            }

            val type = object : TypeToken<List<SignalResult>>() {}.type
            val signalResults: List<SignalResult> = gson.fromJson(hrvData, type)

            if (signalResults.isEmpty()) {
                Logger.w(TAG, msg = "SignalResult数据列表为空，无需上传")
                return true
            }


            Logger.d(TAG, msg = "准备批量上传${signalResults.size}条SignalResult数据")


            // 使用批量上传方法
//            val uploadSuccess = withContext(Dispatchers.IO) {
//                suspendCancellableCoroutine<Boolean> { continuation ->
//                    coroutineScope.launch {
//                        MutableStateFlow(signalResultRepository.uploadSignalResults(signalResults)).collectResponse {
//                            onSuccess = { _, _, _ ->
//                                Logger.d(TAG, msg = "批量上传SignalResult数据成功，共${signalResults.size}条")
//                                continuation.resume(true)
//                            }
//                            onDataEmpty = { _, _ ->
//                                Logger.w(TAG, msg = "批量上传SignalResult数据返回空数据，认为上传成功")
//                                continuation.resume(true)
//                            }
//                            onFailed = { errorCode, errorMsg ->
//                                Logger.e(TAG, msg = "批量上传SignalResult数据失败: errorCode=$errorCode, errorMsg=$errorMsg")
//                                continuation.resume(false)
//                            }
//                            onError = { throwable ->
//                                Logger.e(TAG, msg = "批量上传SignalResult数据异常: ${throwable?.message}")
//                                continuation.resume(false)
//                            }
//                        }
//                    }
//                }
//            }

//            if (uploadSuccess) {
//                Logger.d(TAG, msg = "SignalResult数据批量上传成功")
//            } else {
//                Logger.e(TAG, msg = "SignalResult数据批量上传失败")
//            }
//
//            uploadSuccess

            return true

        } catch (e: Exception) {
            Logger.e(TAG, msg = "从MMKV缓存上传SignalResult数据异常: ${e.message}")
            false
        }
    }



}
