# C/C++ build system timings
generate_cxx_metadata
  [gap of 17ms]
  create-invalidation-state 59ms
  [gap of 34ms]
  write-metadata-json-to-file 27ms
generate_cxx_metadata completed in 139ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 20ms]
  create-invalidation-state 66ms
  [gap of 27ms]
  write-metadata-json-to-file 10ms
generate_cxx_metadata completed in 123ms

# C/C++ build system timings
generate_cxx_metadata
  [gap of 19ms]
  create-invalidation-state 76ms
  [gap of 34ms]
generate_cxx_metadata completed in 129ms

